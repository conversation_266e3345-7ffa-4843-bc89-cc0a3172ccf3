import { NextResponse } from "next/server"
import { getDetailedContactStats } from "@/lib/supabase-database"

export async function GET() {
  try {
    const stats = await getDetailedContactStats()

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error("Error fetching detailed stats:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
