import { NextRequest, NextResponse } from "next/server"
import { updateContactStatus } from "@/lib/supabase-database"

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { status, notes } = await request.json()
    const contactId = parseInt(params.id)

    if (isNaN(contactId)) {
      return NextResponse.json(
        { success: false, error: "ID de contacto inválido" },
        { status: 400 }
      )
    }

    if (!status) {
      return NextResponse.json(
        { success: false, error: "Estado requerido" },
        { status: 400 }
      )
    }

    const validStatuses = ['new', 'contacted', 'in_progress', 'qualified', 'proposal_sent', 'closed_won', 'closed_lost']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: "Estado inválido" },
        { status: 400 }
      )
    }

    const updatedContact = await updateContactStatus(contactId, status, notes)

    return NextResponse.json({
      success: true,
      data: updatedContact,
      message: "Estado del contacto actualizado exitosamente"
    })

  } catch (error) {
    console.error("Error updating contact status:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
