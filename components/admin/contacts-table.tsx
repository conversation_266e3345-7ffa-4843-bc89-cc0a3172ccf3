"use client"

import { useState } from 'react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Eye,
  Mail,
  Phone,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import type { Contact } from '@/lib/supabase'
import ContactStatusManager from './contact-status-manager'

interface ContactsTableProps {
  contacts: Contact[]
  currentPage: number
  totalPages: number
  total: number
  onContactUpdate?: () => void
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  normal: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300',
  high: 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300',
  urgent: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300',
}

const priorityLabels = {
  low: 'Baja',
  normal: 'Normal',
  high: 'Alta',
  urgent: 'Urgente',
}

const statusColors = {
  new: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300',
  contacted: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300',
  in_progress: 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300',
  qualified: 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-300',
  proposal_sent: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-300',
  closed_won: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300',
  closed_lost: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300',
}

const statusLabels = {
  new: 'Nuevo',
  contacted: 'Contactado',
  in_progress: 'En Progreso',
  qualified: 'Calificado',
  proposal_sent: 'Propuesta Enviada',
  closed_won: 'Cerrado Ganado',
  closed_lost: 'Cerrado Perdido',
}

export default function ContactsTable({
  contacts,
  currentPage,
  totalPages,
  total,
  onContactUpdate
}: ContactsTableProps) {

  const handleStatusUpdate = async (contactId: number, newStatus: string, notes?: string) => {
    try {
      const response = await fetch(`/api/admin/contacts/${contactId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus, notes }),
      })

      if (response.ok) {
        // Refresh the contacts list
        if (onContactUpdate) {
          onContactUpdate()
        }
      } else {
        throw new Error('Error updating contact status')
      }
    } catch (error) {
      console.error('Error updating contact status:', error)
      throw error
    }
  }
  const [selectedContacts, setSelectedContacts] = useState<number[]>([])

  const toggleContact = (id: number) => {
    setSelectedContacts(prev => 
      prev.includes(id) 
        ? prev.filter(contactId => contactId !== id)
        : [...prev, id]
    )
  }

  const toggleAll = () => {
    setSelectedContacts(prev => 
      prev.length === contacts.length 
        ? [] 
        : contacts.map(contact => contact.id)
    )
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={selectedContacts.length === contacts.length && contacts.length > 0}
                  onChange={toggleAll}
                  className="rounded border-slate-300"
                />
              </TableHead>
              <TableHead>Contacto</TableHead>
              <TableHead>Servicio</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Prioridad</TableHead>
              <TableHead>Fecha</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {contacts.map((contact) => (
              <TableRow key={contact.id}>
                <TableCell>
                  <input
                    type="checkbox"
                    checked={selectedContacts.includes(contact.id)}
                    onChange={() => toggleContact(contact.id)}
                    className="rounded border-slate-300"
                  />
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium text-slate-900 dark:text-white">
                      {contact.name}
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                      <Mail className="w-3 h-3" />
                      <span>{contact.email}</span>
                    </div>
                    {contact.phone && (
                      <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                        <Phone className="w-3 h-3" />
                        <span>{contact.phone}</span>
                      </div>
                    )}
                    {contact.company && (
                      <div className="text-sm text-slate-500 dark:text-slate-500">
                        {contact.company}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {contact.service}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    className={`text-xs ${statusColors[(contact.status || 'new') as keyof typeof statusColors]}`}
                  >
                    {statusLabels[(contact.status || 'new') as keyof typeof statusLabels]}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    className={`text-xs ${priorityColors[contact.priority as keyof typeof priorityColors]}`}
                  >
                    {priorityLabels[contact.priority as keyof typeof priorityLabels]}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div className="text-slate-900 dark:text-white">
                      {new Date(contact.created_at).toLocaleDateString('es-ES', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </div>
                    <div className="text-slate-500 dark:text-slate-500">
                      {new Date(contact.created_at).toLocaleTimeString('es-ES', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <ContactStatusManager
                      contact={contact}
                      onStatusUpdate={handleStatusUpdate}
                    />
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/contacts/${contact.id}`}>
                            <Eye className="w-4 h-4 mr-2" />
                            Ver Detalles
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Mail className="w-4 h-4 mr-2" />
                          Enviar Email
                        </DropdownMenuItem>
                        {contact.phone && (
                          <DropdownMenuItem>
                            <Phone className="w-4 h-4 mr-2" />
                            Llamar
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-slate-600 dark:text-slate-400">
          Mostrando {((currentPage - 1) * 20) + 1} a {Math.min(currentPage * 20, total)} de {total} contactos
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage <= 1}
            asChild={currentPage > 1}
          >
            {currentPage > 1 ? (
              <Link href={`/admin/contacts?page=${currentPage - 1}`}>
                <ChevronLeft className="w-4 h-4 mr-2" />
                Anterior
              </Link>
            ) : (
              <>
                <ChevronLeft className="w-4 h-4 mr-2" />
                Anterior
              </>
            )}
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  asChild
                >
                  <Link href={`/admin/contacts?page=${page}`}>
                    {page}
                  </Link>
                </Button>
              )
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage >= totalPages}
            asChild={currentPage < totalPages}
          >
            {currentPage < totalPages ? (
              <Link href={`/admin/contacts?page=${currentPage + 1}`}>
                Siguiente
                <ChevronRight className="w-4 h-4 ml-2" />
              </Link>
            ) : (
              <>
                Siguiente
                <ChevronRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedContacts.length > 0 && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-slate-900 dark:bg-slate-800 text-white rounded-lg shadow-lg p-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm">
              {selectedContacts.length} contacto{selectedContacts.length !== 1 ? 's' : ''} seleccionado{selectedContacts.length !== 1 ? 's' : ''}
            </span>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="secondary">
                <Mail className="w-4 h-4 mr-2" />
                Enviar Email
              </Button>
              <Button size="sm" variant="secondary">
                Exportar
              </Button>
              <Button 
                size="sm" 
                variant="ghost"
                onClick={() => setSelectedContacts([])}
              >
                Cancelar
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
