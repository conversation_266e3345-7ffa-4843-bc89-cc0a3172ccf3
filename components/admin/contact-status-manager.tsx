'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Edit, 
  Save, 
  X,
  User,
  Phone,
  Mail,
  Building,
  MessageSquare,
  Calendar,
  Euro
} from 'lucide-react'

interface Contact {
  id: number
  name: string
  email: string
  phone: string
  company: string
  service: string
  message: string
  budget: string
  timeline: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
}

interface ContactStatusManagerProps {
  contact: Contact
  onStatusUpdate: (contactId: number, newStatus: string, notes?: string) => void
}

const statusOptions = [
  { value: 'new', label: 'Nuevo', color: 'bg-blue-100 text-blue-800' },
  { value: 'contacted', label: 'Contactado', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'in_progress', label: 'En Progreso', color: 'bg-orange-100 text-orange-800' },
  { value: 'qualified', label: 'Calificado', color: 'bg-purple-100 text-purple-800' },
  { value: 'proposal_sent', label: 'Propuesta Enviada', color: 'bg-indigo-100 text-indigo-800' },
  { value: 'closed_won', label: 'Cerrado Ganado', color: 'bg-green-100 text-green-800' },
  { value: 'closed_lost', label: 'Cerrado Perdido', color: 'bg-red-100 text-red-800' }
]

const getStatusInfo = (status: string) => {
  return statusOptions.find(option => option.value === status) || statusOptions[0]
}

export default function ContactStatusManager({ contact, onStatusUpdate }: ContactStatusManagerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState(contact.status || 'new')
  const [notes, setNotes] = useState(contact.notes || '')
  const [saving, setSaving] = useState(false)

  const handleSave = async () => {
    setSaving(true)
    try {
      await onStatusUpdate(contact.id, selectedStatus, notes)
      setIsOpen(false)
    } catch (error) {
      console.error('Error updating status:', error)
    } finally {
      setSaving(false)
    }
  }

  const statusInfo = getStatusInfo(contact.status || 'new')

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Edit className="w-4 h-4" />
          Gestionar
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Gestionar Contacto: {contact.name}
          </DialogTitle>
          <DialogDescription>
            Actualiza el estado y agrega notas sobre este contacto
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Mail className="w-4 h-4 text-slate-500" />
                <span className="font-medium">Email:</span>
                <span>{contact.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="w-4 h-4 text-slate-500" />
                <span className="font-medium">Teléfono:</span>
                <span>{contact.phone}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Building className="w-4 h-4 text-slate-500" />
                <span className="font-medium">Empresa:</span>
                <span>{contact.company}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <MessageSquare className="w-4 h-4 text-slate-500" />
                <span className="font-medium">Servicio:</span>
                <span>{contact.service}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Euro className="w-4 h-4 text-slate-500" />
                <span className="font-medium">Presupuesto:</span>
                <span>{contact.budget}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-slate-500" />
                <span className="font-medium">Timeline:</span>
                <span>{contact.timeline}</span>
              </div>
            </div>
          </div>

          {/* Message */}
          <div>
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Mensaje del Cliente
            </label>
            <div className="mt-1 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg text-sm">
              {contact.message}
            </div>
          </div>

          {/* Current Status */}
          <div>
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Estado Actual
            </label>
            <div className="mt-1">
              <Badge className={statusInfo.color}>
                {statusInfo.label}
              </Badge>
            </div>
          </div>

          {/* Status Update */}
          <div>
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Nuevo Estado
            </label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Seleccionar estado" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${option.color.split(' ')[0]}`} />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div>
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Notas Internas
            </label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Agregar notas sobre el seguimiento, conversaciones, próximos pasos..."
              rows={4}
              className="mt-1"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            <X className="w-4 h-4 mr-2" />
            Cancelar
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Guardando...' : 'Guardar Cambios'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
