# 🚀 Karedesk - Soluciones Digitales del Futuro

<div align="center">
  <img src="./public/logo.png" alt="Karedesk Logo" width="120" height="120">

  **Transformamos tu negocio con tecnología de vanguardia**

  [![Netlify Status](https://api.netlify.com/api/v1/badges/your-badge-id/deploy-status)](https://app.netlify.com/sites/fastidious-centaur-160402/deploys)
  [![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black)](https://nextjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4-38bdf8)](https://tailwindcss.com/)
  [![Supabase](https://img.shields.io/badge/Supabase-Database-green)](https://supabase.com/)
</div>

## 📋 Tabla de Contenidos

- [🎯 Características](#-características)
- [🛠️ Stack Tecnológico](#️-stack-tecnológico)
- [🚀 Inicio Rápido](#-inicio-rápido)
- [⚙️ Configuración](#️-configuración)
- [🏗️ Arquitectura](#️-arquitectura)
- [🧪 Testing](#-testing)
- [🚀 Deployment](#-deployment)
- [📊 Monitoreo](#-monitoreo)
- [🤝 Contribución](#-contribución)

## 🎯 Características

### 🌟 Funcionalidades Principales
- ✅ **Sitio Web Corporativo** - Landing page moderna y responsive
- ✅ **Sistema de Contacto** - Formularios inteligentes con validación
- ✅ **Panel de Administración** - Dashboard completo para gestión
- ✅ **Autenticación Segura** - Sistema de login con JWT
- ✅ **Base de Datos** - Integración completa con Supabase
- ✅ **Email Service** - Notificaciones automáticas con Resend
- ✅ **SEO Optimizado** - Meta tags, sitemap, robots.txt
- ✅ **PWA Ready** - Características de aplicación web progresiva

### 🎨 Servicios Ofrecidos
- 🔧 **Asistencia Informática** - Soporte técnico especializado
- 🛡️ **Análisis de Vulnerabilidades** - Auditorías de seguridad
- 🧠 **Consultoría IA** - Implementación de inteligencia artificial
- 💻 **Creación de Páginas Web** - Desarrollo web moderno

## 🛠️ Stack Tecnológico

### Frontend
- **Framework**: Next.js 15.2.4 (App Router)
- **Lenguaje**: TypeScript 5.0
- **Estilos**: Tailwind CSS 3.4
- **Componentes**: Radix UI + shadcn/ui
- **Animaciones**: Framer Motion
- **Iconos**: Lucide React

### Backend
- **API**: Next.js API Routes
- **Base de Datos**: Supabase (PostgreSQL)
- **Autenticación**: Supabase Auth + JWT
- **Email**: Resend API
- **Validación**: Zod

### DevOps & Deployment
- **Hosting**: Netlify
- **CI/CD**: GitHub Actions + Netlify
- **Monitoreo**: Netlify Analytics
- **Dominio**: karedesk.es (configurado)

## 🚀 Inicio Rápido

### Prerrequisitos
- Node.js 18+
- pnpm (recomendado) o npm
- Cuenta en Supabase
- Cuenta en Resend

### 1. Clonar el repositorio
\`\`\`bash
git clone https://github.com/stoja88/KaredeskFinal.git
cd KaredeskFinal
\`\`\`

### 2. Instalar dependencias
\`\`\`bash
pnpm install
# o
npm install
\`\`\`

### 3. Configurar variables de entorno
Crea un archivo \`.env.local\` en la raíz del proyecto:

\`\`\`bash
# Supabase Configuration (CONFIGURADO)
NEXT_PUBLIC_SUPABASE_URL=https://hswatweayaatytzzyrdg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhzd2F0d2VheWFhdHl0enp5cmRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyMjIwNDgsImV4cCI6MjA2MDc5ODA0OH0.B8DLy22f0KRQsJlout0QEoAAlOV9xvZTVXstXW1ueC8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhzd2F0d2VheWFhdHl0enp5cmRnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTIyMjA0OCwiZXhwIjoyMDYwNzk4MDQ4fQ.qZfTiqPW0goXgQArhNhj60kM7lqbWSORljZwlf7gUCM

# Resend Configuration (CONFIGURAR CON TU API KEY)
RESEND_API_KEY=re_tu_api_key_aqui

# Email Configuration
CONTACT_EMAIL=<EMAIL>
SALES_EMAIL=<EMAIL>
SYSTEM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# Development
NODE_ENV=development
\`\`\`

### 4. Configurar base de datos
✅ **Base de datos ya configurada:**
- Tabla `contacts` creada con todos los campos necesarios
- Índices optimizados para rendimiento
- Row Level Security (RLS) habilitado
- Triggers automáticos para `updated_at`
- Datos de prueba insertados

\`\`\`bash
# Si necesitas reconfigurar, ejecuta:
# Los scripts están en ./scripts/supabase-setup.sql
\`\`\`

### 5. Ejecutar en desarrollo
\`\`\`bash
pnpm dev
# o
npm run dev
\`\`\`

Visita [http://localhost:3000](http://localhost:3000) para ver la aplicación.

### 🔐 Credenciales de Administrador
- **Email:** <EMAIL>
- **Password:** admin123
- **Panel Admin:** [http://localhost:3000/admin](http://localhost:3000/admin)

### ✅ Funcionalidades Verificadas
- ✅ Página principal carga correctamente
- ✅ Formulario de contacto funciona y guarda en BD
- ✅ Autenticación de administrador operativa
- ✅ Páginas de servicios accesibles
- ✅ Base de datos Supabase conectada
- ✅ API endpoints respondiendo correctamente

## ⚙️ Configuración

### 🗄️ Supabase Setup
1. Crea un proyecto en [supabase.com](https://supabase.com)
2. Obtén las credenciales del proyecto
3. Ejecuta los scripts SQL en \`./scripts/\`
4. Configura las variables de entorno

### 📧 Resend Setup
1. Crea una cuenta en [resend.com](https://resend.com)
2. Obtén tu API key
3. (Opcional) Configura tu dominio personalizado
4. Actualiza las variables de entorno

### 🔐 Autenticación
- Sistema basado en JWT con Supabase Auth
- Middleware de protección para rutas admin
- Sesiones persistentes con cookies seguras

## 🏗️ Arquitectura

### 📁 Estructura del Proyecto
\`\`\`
├── app/                    # App Router (Next.js 13+)
│   ├── admin/             # Panel de administración
│   ├── api/               # API Routes
│   ├── login/             # Página de login
│   ├── servicios/         # Páginas de servicios
│   └── globals.css        # Estilos globales
├── components/            # Componentes reutilizables
│   ├── admin/            # Componentes del admin
│   ├── ui/               # Componentes base (shadcn/ui)
│   └── ...
├── lib/                  # Utilidades y configuraciones
│   ├── supabase.ts       # Cliente de Supabase
│   ├── email-service.ts  # Servicio de emails
│   └── ...
├── hooks/                # Custom React hooks
├── public/               # Assets estáticos
├── scripts/              # Scripts de base de datos
└── styles/               # Estilos adicionales
\`\`\`

### 🔄 Flujo de Datos
1. **Frontend** → Next.js con TypeScript
2. **API Layer** → Next.js API Routes
3. **Database** → Supabase PostgreSQL
4. **Auth** → Supabase Auth + JWT
5. **Email** → Resend API
6. **Deploy** → Netlify

## 🧪 Testing

### Scripts de Testing Disponibles
\`\`\`bash
# Verificación de tipos
pnpm type-check

# Linting
pnpm lint

# Build de producción
pnpm build

# Testing manual de APIs
node test-final-deployment.js
\`\`\`

### Testing Manual
- \`test-final-deployment.js\` - Pruebas de deployment
- \`test-auth-fix.js\` - Pruebas de autenticación
- \`test-contact-form.js\` - Pruebas de formularios

## 🚀 Deployment

### Netlify (Recomendado)
1. Conecta tu repositorio de GitHub
2. Configura las variables de entorno en Netlify
3. Deploy automático en cada push a main

### Variables de Entorno en Producción
\`\`\`bash
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=tu_supabase_service_role_key
RESEND_API_KEY=re_production_key
CONTACT_EMAIL=<EMAIL>
SALES_EMAIL=<EMAIL>
SYSTEM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
NODE_ENV=production
\`\`\`

### Comandos de Build
\`\`\`bash
# Build local
pnpm build

# Iniciar servidor de producción
pnpm start

# Preview del build
pnpm build && pnpm start
\`\`\`

## 📊 Monitoreo

### 📈 Analytics
- Netlify Analytics integrado
- Métricas de rendimiento
- Logs de errores

### 📧 Email Monitoring
- Dashboard de Resend para métricas de email
- Logs de entrega y apertura
- Debugging de errores de envío

### 🔍 Debugging
\`\`\`bash
# Logs de desarrollo
console.log disponibles en desarrollo

# Logs de producción
Netlify Functions logs
\`\`\`

## 🤝 Contribución

### Workflow de Desarrollo
1. Fork el repositorio
2. Crea una rama feature: \`git checkout -b feature/nueva-funcionalidad\`
3. Commit tus cambios: \`git commit -m 'Add nueva funcionalidad'\`
4. Push a la rama: \`git push origin feature/nueva-funcionalidad\`
5. Abre un Pull Request

### Estándares de Código
- TypeScript estricto
- ESLint + Prettier
- Convenciones de naming consistentes
- Comentarios en español para funciones complejas

### Testing
- Prueba todas las funcionalidades antes del PR
- Incluye tests para nuevas features
- Verifica que el build pase sin errores

## 📄 Licencia

Este proyecto es privado y pertenece a Karedesk.

## 📞 Contacto

- **Website**: [https://fastidious-centaur-160402.netlify.app/](https://fastidious-centaur-160402.netlify.app/)
- **Email**: <EMAIL>
- **GitHub**: [@stoja88](https://github.com/stoja88)

---

<div align="center">
  <strong>Desarrollado con ❤️ por el equipo de Karedesk</strong>
</div>
