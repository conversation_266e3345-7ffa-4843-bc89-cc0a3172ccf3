-- Agregar columnas de estado y notas a la tabla contacts
ALTER TABLE contacts 
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'new',
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Crear índice para el estado
CREATE INDEX IF NOT EXISTS idx_contacts_status ON contacts(status);

-- Comentarios para documentar las columnas
COMMENT ON COLUMN contacts.status IS 'Estado del contacto: new, contacted, in_progress, qualified, proposal_sent, closed_won, closed_lost';
COMMENT ON COLUMN contacts.notes IS 'Notas internas sobre el contacto y seguimiento';

-- Actualizar contactos existentes con estado 'new' por defecto
UPDATE contacts 
SET status = 'new' 
WHERE status IS NULL;
