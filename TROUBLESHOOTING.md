# 🔧 Troubleshooting - Karedesk Portal

## 🚨 Problemas Comunes y Soluciones

### 1. Error: "Invalid API key" en Supabase

**Síntomas:**
```
Error [AuthApiError]: Invalid API key
Supabase login error: Invalid API key
```

**Solución:**
```bash
# 1. Verificar variables de entorno
cat .env.local | grep SUPABASE

# 2. Asegurar que tienes las claves correctas:
NEXT_PUBLIC_SUPABASE_URL=https://hswatweayaatytzzyrdg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 3. Reiniciar el servidor
npm run dev
```

### 2. Formulario de contacto no funciona

**Síntomas:**
```
POST /api/contact 400
Error al guardar la consulta
```

**Diagnóstico:**
```bash
# Probar el endpoint directamente
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test",
    "email": "<EMAIL>",
    "phone": "+34123456789",
    "company": "Test Co",
    "service": "Desarrollo Web",
    "message": "Test message",
    "budget": "1000-5000",
    "timeline": "flexible"
  }'
```

**Soluciones:**
1. Verificar conexión a Supabase
2. Comprobar que la tabla `contacts` existe
3. Verificar permisos RLS
4. Revisar logs del servidor

### 3. Servidor no inicia

**Síntomas:**
```
Error: Cannot find module
EADDRINUSE: address already in use :::3000
```

**Soluciones:**
```bash
# Verificar si el puerto está en uso
lsof -i :3000

# Matar proceso si es necesario
kill -9 $(lsof -t -i:3000)

# Reinstalar dependencias si es necesario
rm -rf node_modules package-lock.json
npm install

# Iniciar servidor
npm run dev
```

### 4. Error de autenticación admin

**Síntomas:**
```
Supabase login error: Invalid API key
POST /api/auth/login 401
```

**Verificación:**
```bash
# Probar login directamente
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'
```

**Soluciones:**
1. Verificar que el usuario admin existe en Supabase
2. Comprobar service role key
3. Verificar configuración de auth en Supabase

### 5. Páginas 404 en servicios

**Síntomas:**
```
GET /servicios/desarrollo-web 404
```

**Verificación:**
```bash
# Comprobar estructura de archivos
ls -la app/servicios/

# Verificar que existen los archivos page.tsx
find app/servicios -name "page.tsx"
```

**Solución:**
Verificar que existen los archivos de página en la estructura correcta.

### 6. Error de compilación TypeScript

**Síntomas:**
```
Type error: Cannot find module
Property 'X' does not exist on type 'Y'
```

**Soluciones:**
```bash
# Verificar tipos
npm run type-check

# Reinstalar dependencias de tipos
npm install @types/node @types/react @types/react-dom

# Limpiar cache de Next.js
rm -rf .next
npm run dev
```

### 7. Error de base de datos

**Síntomas:**
```
Error saving contact to Supabase
Connection timeout
```

**Diagnóstico:**
```bash
# Verificar conexión a Supabase
curl -H "apikey: TU_ANON_KEY" \
     -H "Authorization: Bearer TU_ANON_KEY" \
     https://hswatweayaatytzzyrdg.supabase.co/rest/v1/contacts?select=*&limit=1
```

**Soluciones:**
1. Verificar conectividad a internet
2. Comprobar estado del proyecto Supabase
3. Verificar configuración de RLS
4. Revisar logs de Supabase

## 🔍 Comandos de Diagnóstico

### Verificar estado general
```bash
# Estado del servidor
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000

# Estado de la API
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/contact

# Verificar variables de entorno
env | grep -E "(SUPABASE|RESEND|EMAIL)"
```

### Logs útiles
```bash
# Logs del servidor de desarrollo
npm run dev

# Logs de build
npm run build

# Verificar sintaxis
npm run lint
```

### Verificar base de datos
```bash
# Conectar a Supabase y verificar tabla
# (usar el dashboard de Supabase o SQL editor)
SELECT COUNT(*) FROM contacts;
SELECT * FROM contacts ORDER BY created_at DESC LIMIT 5;
```

## 📊 Monitoreo de Estado

### Endpoints de salud
```bash
# Página principal
curl -s -w "%{http_code}" http://localhost:3000 -o /dev/null

# API de contacto
curl -s -w "%{http_code}" http://localhost:3000/api/contact -o /dev/null

# Login admin
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  -s -w "%{http_code}" -o /dev/null
```

### Verificar configuración
```bash
# Verificar archivo de configuración
cat .env.local

# Verificar package.json
cat package.json | jq '.dependencies'

# Verificar estructura del proyecto
tree -I 'node_modules|.next|.git' -L 3
```

## 🆘 Contacto de Soporte

Si los problemas persisten:

1. **Revisar logs completos** del servidor de desarrollo
2. **Documentar el error** con mensajes exactos
3. **Verificar configuración** de variables de entorno
4. **Comprobar conectividad** a servicios externos (Supabase)

### Información útil para reportar problemas:
- Versión de Node.js: `node --version`
- Versión de npm: `npm --version`
- Sistema operativo
- Mensaje de error completo
- Pasos para reproducir el problema

---
**Última actualización:** 29 de junio de 2025
